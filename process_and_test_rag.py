#!/usr/bin/env python3
"""
Enhanced RAG System Setup and Testing Script

This script processes the PDF tutorial with enhanced multimodal analysis
and tests the RAG system with various query types.
"""

import os
import sys
from dotenv import load_dotenv

# Add the magonia module to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def process_pdf_tutorial():
    """Process the PDF tutorial with enhanced multimodal analysis."""
    print("🔄 Processing PDF tutorial with enhanced multimodal analysis...")
    
    try:
        from scripts.process_tutorial_multimodal import MultimodalTutorialProcessor
        
        processor = MultimodalTutorialProcessor()
        
        # Check if PDF exists
        pdf_path = "Agrisense plateform tutorial-ENG.pdf"
        if not os.path.exists(pdf_path):
            print(f"❌ PDF file not found: {pdf_path}")
            return False
        
        print(f"📄 Processing PDF: {pdf_path}")
        content_blocks = processor.extract_pdf_content(pdf_path)
        
        print(f"✅ Extracted {len(content_blocks)} content blocks")
        
        # Create both formats
        processor.create_structured_tutorial(content_blocks, "tutorial-ENG-multimodal.txt")
        processor.create_enhanced_embeddings(content_blocks, "tutorial-ENG-enhanced.txt")
        
        print("✅ Created tutorial files:")
        print("   - tutorial-ENG-multimodal.txt (detailed format)")
        print("   - tutorial-ENG-enhanced.txt (optimized for RAG)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_rag_system():
    """Test the enhanced RAG system with various query types."""
    print("\n🧪 Testing Enhanced RAG System")
    print("=" * 60)
    
    try:
        from magonia.gpt4o_direct import GPT4oDirectTools
        
        # Initialize the agent
        agent = GPT4oDirectTools(
            enable_translation=False,
            enable_context_analysis=False
        )
        
        # Test questions covering different aspects
        test_questions = [
            # UI/Interface questions
            {
                "category": "UI Navigation",
                "questions": [
                    "What does the main dashboard look like?",
                    "How do I find the irrigation button in the app?",
                    "Where is the login screen and what do I need to enter?",
                    "What buttons are available on the field management screen?"
                ]
            },
            # Step-by-step instructions
            {
                "category": "Instructions",
                "questions": [
                    "How do I create a new field in the app?",
                    "What steps do I follow to set up irrigation scheduling?",
                    "How do I add a new irrigation zone?",
                    "How do I view my field's irrigation history?"
                ]
            },
            # Feature explanations
            {
                "category": "Features",
                "questions": [
                    "What irrigation controls are available?",
                    "How does the weather integration work?",
                    "What data can I see in the reports section?",
                    "How do soil moisture sensors connect to the app?"
                ]
            }
        ]
        
        user_id = "test-user-enhanced-rag"
        
        for category_info in test_questions:
            category = category_info["category"]
            questions = category_info["questions"]
            
            print(f"\n📋 Testing {category} Questions")
            print("-" * 40)
            
            for i, question in enumerate(questions, 1):
                print(f"\n[{i}] 🔍 Question: {question}")
                
                try:
                    response = agent.ask(
                        question=question,
                        chat_history="",
                        user_id=user_id
                    )
                    
                    print(f"🤖 Response: {response}")
                    
                    # Analyze response quality
                    response_lower = str(response).lower()
                    quality_indicators = {
                        "visual_guide": "📱" in str(response) or "visual guide" in response_lower,
                        "step_by_step": "📋" in str(response) or "step" in response_lower,
                        "ui_elements": "button" in response_lower or "menu" in response_lower or "screen" in response_lower,
                        "actionable": any(word in response_lower for word in ["click", "tap", "press", "select", "navigate"]),
                        "structured": "**" in str(response) or "###" in str(response)
                    }
                    
                    quality_score = sum(quality_indicators.values())
                    print(f"📊 Response Quality: {quality_score}/5")
                    
                    if quality_indicators["visual_guide"]:
                        print("   ✅ Contains visual guidance")
                    if quality_indicators["step_by_step"]:
                        print("   ✅ Provides step-by-step instructions")
                    if quality_indicators["ui_elements"]:
                        print("   ✅ Mentions specific UI elements")
                    if quality_indicators["actionable"]:
                        print("   ✅ Includes actionable instructions")
                    if quality_indicators["structured"]:
                        print("   ✅ Well-structured response")
                        
                except Exception as e:
                    print(f"❌ Error: {str(e)}")
                
                print("-" * 30)
        
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all dependencies are installed and the magonia module is accessible")
        return False
    except Exception as e:
        print(f"❌ System Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_store_directly():
    """Test the enhanced vector store directly."""
    print("\n🔍 Testing Enhanced Vector Store Directly")
    print("=" * 50)
    
    try:
        from magonia.enhanced_vector_store import EnhancedVectorStoreRetriever
        
        redis_url = os.getenv("REDIS_URL")
        if not redis_url:
            print("❌ REDIS_URL not configured")
            return False
        
        # Test enhanced retriever
        retriever = EnhancedVectorStoreRetriever(redis_url, "test-enhanced-tutorial")
        
        # Try to load enhanced tutorial if it exists
        if os.path.exists("tutorial-ENG-enhanced.txt"):
            print("📚 Loading enhanced tutorial...")
            retriever.load_enhanced_tutorial("tutorial-ENG-enhanced.txt")
            print("✅ Enhanced tutorial loaded successfully")
            
            # Test different query types
            test_queries = [
                ("How do I login to the app?", "UI guidance"),
                ("What steps to create a field?", "Instructions"),
                ("Dashboard interface elements", "UI elements")
            ]
            
            for query, query_type in test_queries:
                print(f"\n🔍 Testing: {query} ({query_type})")
                results = retriever.smart_lookup(query, k=2)
                
                for i, result in enumerate(results):
                    print(f"   Result {i+1}: {result.page_content[:100]}...")
                    metadata = getattr(result, 'metadata', {})
                    if metadata:
                        print(f"   Metadata: {metadata}")
        else:
            print("⚠️ Enhanced tutorial file not found, skipping direct vector store test")
            
        return True
        
    except Exception as e:
        print(f"❌ Vector store test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main execution function."""
    print("🚀 Enhanced RAG System Setup and Testing")
    print("=" * 60)
    
    # Step 1: Process PDF tutorial
    pdf_success = process_pdf_tutorial()
    
    if not pdf_success:
        print("\n⚠️ PDF processing failed, but continuing with existing files...")
    
    # Step 2: Test vector store directly
    vector_success = test_vector_store_directly()
    
    # Step 3: Test full RAG system
    rag_success = test_enhanced_rag_system()
    
    # Summary
    print("\n📋 Test Summary")
    print("=" * 30)
    print(f"PDF Processing: {'✅ Success' if pdf_success else '❌ Failed'}")
    print(f"Vector Store: {'✅ Success' if vector_success else '❌ Failed'}")
    print(f"RAG System: {'✅ Success' if rag_success else '❌ Failed'}")
    
    if pdf_success and vector_success and rag_success:
        print("\n🎉 All tests passed! Your enhanced RAG system is ready.")
        print("\n📝 Next Steps:")
        print("1. The system can now understand PDF screenshots and provide UI guidance")
        print("2. Test with your own questions about the application")
        print("3. The system will provide visual guides and step-by-step instructions")
    else:
        print("\n⚠️ Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    main()
