import subprocess
import sys

def run_test():
    """Run the test and capture output."""
    try:
        result = subprocess.run([
            sys.executable, "test_current_system.py"
        ], capture_output=True, text=True, timeout=120)
        
        print("🧪 TEST RESULTS:")
        print("=" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("\n❌ ERRORS:")
            print(result.stderr)
            
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 2 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

if __name__ == "__main__":
    success = run_test()
    if success:
        print("\n✅ Test completed successfully")
    else:
        print("\n❌ Test failed or encountered errors")