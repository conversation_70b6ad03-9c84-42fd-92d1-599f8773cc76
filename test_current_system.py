import os
import sys
from dotenv import load_dotenv

# Add the magonia module to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_current_tutorial_functionality():
    """Test the current system with tutorial-related questions."""
    
    # Import after path setup
    try:
        from magonia.gpt4o_direct import GPT4oDirectTools
        
        # Initialize the agent
        agent = GPT4oDirectTools(
            enable_translation=False,
            enable_context_analysis=False
        )
        
        # Test questions that should use the lookup tool
        tutorial_questions = [
            "How do I create a new field in the app?",
            "What steps do I follow to set up irrigation scheduling?",
            "How do I view my field's irrigation history?",
            "Where can I find the weather data in the application?",
            "What does the main dashboard look like?",
            "How do I find the irrigation button in the app?"
        ]
        
        user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
        
        print("🧪 Testing Current System with Tutorial Questions")
        print("=" * 60)
        
        for i, question in enumerate(tutorial_questions, 1):
            print(f"\n[{i}] 🔍 Question: {question}")
            
            try:
                response = agent.ask(
                    question=question,
                    chat_history="",
                    user_id=user_id
                )
                
                print(f"🤖 Response: {response}")
                
                # Check if lookup tool was used
                if "lookup_document_tool" in str(response).lower() or "knowledge base" in str(response).lower():
                    print("✅ Tool Usage: Document lookup was attempted")
                else:
                    print("❌ Tool Usage: No document lookup detected")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")
            
            print("-" * 50)
            
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all dependencies are installed and the magonia module is accessible")
    except Exception as e:
        print(f"❌ System Error: {e}")

def test_multimodal_simulation():
    """Simulate how the multimodal system would work."""
    
    print("\n🎯 Simulating Multimodal Tutorial Integration")
    print("=" * 60)
    
    # Simulate what the enhanced system would return
    simulated_responses = {
        "What does the main dashboard look like?": """
**Agrisense Mobile App Guide:**

📱 **Visual Guide:** The main dashboard displays a clean interface with:
- Top navigation bar showing "Agrisense" logo
- Weather widget in the upper right corner showing current conditions
- Central map view displaying your fields with color-coded irrigation status
- Bottom navigation with 5 main tabs: Home, Fields, Irrigation, Reports, Settings
- Quick action buttons for "Add Field" and "Schedule Irrigation"
- Status indicators showing active/inactive irrigation zones

📖 **Instructions:** From the main dashboard, you can access all primary functions. The map provides an overview of your agricultural areas, while the bottom navigation allows quick access to detailed field management, irrigation controls, and reporting features.
""",
        
        "How do I find the irrigation button in the app?": """
**Agrisense Mobile App Guide:**

📱 **Visual Guide:** The irrigation controls are accessible through multiple locations:
- Main "Irrigation" tab in the bottom navigation bar (water drop icon)
- Quick action button labeled "Schedule Irrigation" on the dashboard
- Individual field cards show irrigation status with toggle switches
- Settings menu contains "Irrigation Preferences" option

📖 **Instructions:** 
1. Tap the "Irrigation" tab at the bottom of the screen (third icon from left)
2. This opens the irrigation management interface
3. From here you can view current schedules, create new irrigation events, and modify existing settings
4. Each field shows its irrigation status with visual indicators (green=active, gray=inactive)
"""
    }
    
    test_questions = [
        "What does the main dashboard look like?",
        "How do I find the irrigation button in the app?"
    ]
    
    for question in test_questions:
        print(f"\n🔍 Question: {question}")
        if question in simulated_responses:
            print(f"🤖 Enhanced Response: {simulated_responses[question]}")
        print("-" * 50)

if __name__ == "__main__":
    # Test current system
    test_current_tutorial_functionality()
    
    # Show simulation of enhanced system
    test_multimodal_simulation()
    
    print("\n📋 Next Steps:")
    print("1. Process the tutorial PDF with image analysis")
    print("2. Update the vector store with multimodal content")
    print("3. Test the enhanced system with visual questions")
    print("4. Verify UI guidance accuracy")