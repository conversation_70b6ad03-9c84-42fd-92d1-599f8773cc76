def test_multimodal_tutorial_questions():
    """Test questions that require both text and visual understanding."""
    
    visual_questions = [
        "What does the main dashboard look like?",
        "How do I find the irrigation button in the app?",
        "how to add a new area ? "
    ]
    
    print("Testing Multimodal Tutorial Questions (Text + Images)")
    print("=" * 60)
    
    for question in visual_questions:
        print(f"\n🔍 Question: {question}")
        response = magonia_instance.ask("", question, "test_user")
        print(f"📱 Response: {response}")
        print("-" * 50)

if __name__ == "__main__":
    test_multimodal_tutorial_questions()