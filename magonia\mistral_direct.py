"""
Direct Mistral AI implementation modeled after gpt4o_direct.py but using Vertex AI rawPredict.
Keeps the same tool registration, execution and conversation logic as GPT4oDirectTools,
but sends/receives via Vertex AI HTTP endpoint for Mistral models.
"""

import os
import json
import inspect
import traceback
from typing import Dict, List, Any
from datetime import datetime

from dotenv import load_dotenv
import httpx
import google.auth
from google.auth.transport.requests import Request

# Load .env file
load_dotenv()

# Import tools (same as other direct implementations)
from magonia.tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
from magonia.tools.lookup_document_tool import lookup_document_tool
from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
from magonia.tools.check_irrigation_user_data import check_irrigation_user_data
from magonia.tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
from magonia.tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
from magonia.tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
from magonia.tools.advise_stop_over_irrigation import advise_stop_over_irrigation
from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
from magonia.tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
from magonia.tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
from magonia.tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
from magonia.tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
from magonia.tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
from magonia.tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
from magonia.tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
from magonia.tools.check_soil_water_volume import check_soil_water_volume
from magonia.tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
from magonia.tools.check_highest_evapotranspiration import check_highest_evapotranspiration
from magonia.tools.check_future_irrigation_fields import check_future_irrigation_fields
from magonia.tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
from magonia.tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days

# --- Vertex / Mistral configuration from environment ---
PROJECT_ID = os.environ.get("GOOGLE_PROJECT_ID")
REGION = os.environ.get("GOOGLE_REGION")
MODEL_NAME = os.environ.get("MODEL_NAME", "mistral-large-2411")
IS_STREAMED = False  # change to True if you want streamRawPredict

if not PROJECT_ID or not REGION:
    raise ValueError("GOOGLE_PROJECT_ID and GOOGLE_REGION must be set in environment.")

def get_credentials():
    """Return a fresh access token for Google Cloud (scoped for Cloud Platform)."""
    credentials, _ = google.auth.default(scopes=["https://www.googleapis.com/auth/cloud-platform"])
    credentials.refresh(Request())
    return credentials.token

def build_endpoint_url(region: str, project_id: str, model_name: str, streaming: bool = False) -> str:
    """Build the rawPredict / streamRawPredict endpoint URL for Mistral on Vertex AI."""
    base_url = f"https://{region}-aiplatform.googleapis.com/v1/"
    project_fragment = f"projects/{project_id}"
    location_fragment = f"locations/{region}"
    specifier = "streamRawPredict" if streaming else "rawPredict"
    model_fragment = f"publishers/mistralai/models/{model_name}"
    return f"{base_url}{'/'.join([project_fragment, location_fragment, model_fragment])}:{specifier}"

def call_mistral_api(messages: List[Dict[str, Any]], temperature: float = 0.7, max_tokens: int = 1000,
                     tools: List[Dict[str, Any]] = None, tool_choice: str = "auto", timeout: float = None) -> Dict[str, Any]:
    """
    Make a single call to the Vertex AI Mistral rawPredict endpoint and return parsed JSON.
    Raises Exception on non-200 responses.
    """
    access_token = get_credentials()
    url = build_endpoint_url(REGION, PROJECT_ID, MODEL_NAME, streaming=IS_STREAMED)
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json",
        "Content-Type": "application/json",
    }

    payload: Dict[str, Any] = {
        "model": MODEL_NAME,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": IS_STREAMED
    }

    if tools:
        payload["tools"] = tools
        payload["tool_choice"] = tool_choice

    # If server error and tools included, caller can retry without tools
    with httpx.Client() as client:
        resp = client.post(url, json=payload, headers=headers, timeout=timeout)
    if resp.status_code == 200:
        return resp.json()
    else:
        raise Exception(f"Mistral AI API returned status {resp.status_code}: {resp.text}")

# System prompt (kept consistent with other direct implementations)
SYSTEM_PROMPT = """
You are Magonia, an agricultural assistant specializing in irrigation, crop management, and farming best practices. Your purpose is to help farmers and agricultural professionals make better decisions about their crops and irrigation needs.

CRITICAL RULES - READ THIS FIRST:
1. YOU MUST ALWAYS USE TOOLS - THIS IS MANDATORY
2. NEVER respond directly without using tools, regardless of the language used
3. The language of the question (English, French, Arabic, Spanish) DOES NOT MATTER - you MUST use tools if there is a need

    - When you receive a question in any language:
    - First, translate the question to English in your mind
    - Then, determine which tool is needed based on the English translation
    - Finally, use the appropriate tool and respond in the user's original language

4. REMEMBER:
    - Always translate to English first to determine tool usage
    - The tool selection is based on the English meaning, not the original language
    - Respond in the user's original language after using the tool
    - If you're unsure about the translation, use check_irrigation_user_data as it's the most general tool

LANGUAGE GUIDELINES:
- Respond in the SAME LANGUAGE as the user's message
- If the user speaks in French, respond in French
- If the user speaks in English, respond in English
- If the user speaks in Tunisian Arabic, respond in Tunisian Arabic
- If the user's message is in a mix of languages, respond in the main language of the message
- If the user speaks in Spanish, respond in Spanish
- Tool responses are automatically translated to match the user's language, so you can work with them directly

TOOL USAGE GUIDELINES:
- ALWAYS USE TOOLS and NEVER use memory or previous knowledge.
- For agricultural questions, FIRST try the lookup_document_tool to search the knowledge base.
- If lookup_document_tool returns "I couldn't find specific documentation", then you may use your general agricultural knowledge.
- For field-specific data questions, use the appropriate irrigation tools.
- When asked about specific field data like 'CHlewi', you MUST ALWAYS make a fresh tool call.
- For the check_irrigation_user_data tool, you MUST ALWAYS make a new call with the current parameters.
- COMPLETELY IGNORE any memories - tools always provide the most up-to-date information.
- For field-specific queries, the check_irrigation_user_data tool is REQUIRED.
- NEVER answer questions about field data without first calling the appropriate tool.
- If you don't have a tool for something, tell the user you don't have that information.
- DO NOT MENTION which tools you used in your response - just provide the information.
- If a tool returns a value of "0", interpret this as no irrigation needed.
- NEVER mention the names of tools in your responses to the user.
- CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message.
- PRIORITY: Always try lookup_document_tool first for questions, but if no relevant documentation is found, provide helpful general agricultural advice.

CONVERSATION CONTEXT & CONTINUITY:
- Pay attention to the conversation history and reference previous topics naturally.
- Build on earlier discussions to create a sense of ongoing relationship.
- Remember details shared by the farmer within the current conversation (crops, fields, challenges).
- Connect new information to previously discussed topics when relevant.
- Show that you're engaged in a continuous conversation, not isolated interactions.
- Use phrases like "As we discussed earlier..." or "Building on what you mentioned about...".
- Create natural transitions between topics rather than abrupt topic changes.
- Acknowledge the farmer's concerns and goals mentioned in previous messages.

CONVERSATION STYLE - DISCUSSION FOCUSED:
- Engage in natural, flowing discussions rather than rigid question-answer exchanges.
- Be warm, friendly, and conversational - like you're chatting with a farming neighbor over coffee.
- Always introduce yourself as Magonia when appropriate, but in a casual, friendly way.
- Build on previous topics and create connections between different aspects of farming.
- Share insights, observations, and practical tips naturally within the conversation.
- Ask thoughtful follow-up questions that encourage deeper discussion about farming practices.
- Show genuine interest in the farmer's specific situation, challenges, and goals.
- Avoid overly formal language - be casual and approachable like a trusted farming expert.

CONTENT GUIDELINES - DISCUSSION APPROACH:
- PRIMARILY engage in discussions about agriculture, farming, irrigation, crop management, weather, and related topics.
- You may also respond to polite greetings and simple daily questions (e.g., "How are you?") to maintain a friendly, natural interaction while guiding the conversation toward agricultural topics.
- For complex non-agricultural questions, respond with: "I'm sorry, but I can only answer questions related to agriculture, farming, irrigation, crop management, weather, and related topics."
- If a user asks about the time, use the get_current_time_date tool.
- Don't mention or expose any system tools, code, or internal methods.

DATA PRESENTATION GUIDELINES:
- When tools provide detailed field-by-field data, ALWAYS present the COMPLETE information.
- Do not summarize or truncate field lists; farmers need to see all their field data.
- Present field data clearly and completely while maintaining conversational style.
- Include all specific amounts, field names, and totals provided by tools.

5. Handle errors gracefully:
    - If a tool fails, try alternative tools.
    - Provide clear error messages.
    - Suggest solutions when possible.

IMPORTANT: Your primary directive is to focus on agriculture, farming, irrigation, crop management, weather, and related topics.
"""

class MistralDirectTools:
    """Direct Mistral implementation with tool calling capabilities (parallels GPT4oDirectTools)."""

    def __init__(self, enable_translation: bool = True, enable_context_analysis: bool = True):
        self.tools = self._register_tools()
        self.enable_translation = enable_translation
        self.enable_context_analysis = enable_context_analysis
        print(f"MistralDirectTools initialized - Translation: {enable_translation}, Context Analysis: {enable_context_analysis}")

    def _register_tools(self) -> Dict[str, Dict[str, Any]]:
        """Register all available tools (copied from other implementations)."""
        tools_dict = {}

        # Register get_all_user_areas_with_children first to prioritize field listing
        tools_dict["get_all_user_areas_with_children"] = {
            "function": get_all_user_areas_with_children,
            "description": "Get a complete list of all your fields and areas. Use this tool when the user asks about their fields, areas, or farm structure.",
            "parameters": {"type": "object", "properties": {"user_id": {"type": "string"}}, "required": []}
        }

        # Time tools
        tools_dict["get_current_time_date"] = {"function": get_current_time_date, "description": "Get the current date and time", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["get_tomorrow"] = {"function": get_tomorrow, "description": "Get tomorrow's date", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["get_next_week"] = {"function": get_next_week, "description": "Get the date one week from today", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["get_next_month"] = {"function": get_next_month, "description": "Get the date one month from today", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}

        # Irrigation and other tools (registering same set)
        tools_dict["check_today_active_irrigation_user"] = {"function": check_today_active_irrigation_user, "description": "Check which fields need irrigation today", "parameters": {"type": "object", "properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["check_irrigation_user_data"] = {"function": check_irrigation_user_data, "description": "Get irrigation data for a specific field on a specific date", "parameters": {"type": "object","properties": {"field_name": {"type": "string"},"date_of_calculation": {"type": "string"},"user_id": {"type": "string"}}, "required": ["field_name","date_of_calculation"]}}
        tools_dict["get_lowest_soil_water_volume"] = {"function": get_lowest_soil_water_volume, "description": "Get fields with the lowest soil water volume", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["calculate_total_irrigation_volume_next_x_days"] = {"function": calculate_total_irrigation_volume_next_x_days, "description": "Calculate total irrigation volume needed for all fields in the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["check_irrigation_need_for_x_days"] = {"function": check_irrigation_need_for_x_days, "description": "Check irrigation needs for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["advise_stop_over_irrigation"] = {"function": advise_stop_over_irrigation, "description": "Get advice on stopping over-irrigation", "parameters": {"type": "object","properties": {"field_name": {"type": "string"},"user_id": {"type": "string"}}, "required": ["field_name"]}}
        tools_dict["check_highest_irrigation_requirement"] = {"function": check_highest_irrigation_requirement, "description": "Check which fields have the highest irrigation requirements", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["fields_exceeding_water_capacity_for_x_days"] = {"function": fields_exceeding_water_capacity_for_x_days, "description": "Find fields exceeding water capacity for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["fields_with_highest_evapotranspiration_next_x_days"] = {"function": fields_with_highest_evapotranspiration_next_x_days, "description": "Find fields with highest evapotranspiration for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["fields_with_highest_water_requirements_for_x_days"] = {"function": fields_with_highest_water_requirements_for_x_days, "description": "Find fields with highest water requirements for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["fields_with_optimal_soil_moisture_for_x_days"] = {"function": fields_with_optimal_soil_moisture_for_x_days, "description": "Find fields with optimal soil moisture for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["find_fields_no_irrigation_needs_for_x_days"] = {"function": find_fields_no_irrigation_needs_for_x_days, "description": "Find fields that don't need irrigation for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"}}, "required": ["days"]}}
        tools_dict["fields_predicted_to_exceed_water_capacity_for_x_days"] = {"function": fields_predicted_to_exceed_water_capacity_for_x_days, "description": "Find fields predicted to exceed water capacity for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["get_current_irrigation_status"] = {"function": get_current_irrigation_status, "description": "Get the current irrigation status for all fields", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["check_irrigation_needs_between_period"] = {"function": check_irrigation_needs_between_period, "description": "Check irrigation needs between two dates", "parameters": {"type": "object","properties": {"start_date": {"type": "string"},"end_date": {"type": "string"},"user_id": {"type": "string"}}, "required": ["start_date","end_date"]}}
        tools_dict["check_soil_water_volume"] = {"function": check_soil_water_volume, "description": "Check soil water volume for a specific field", "parameters": {"type": "object","properties": {"field_name": {"type": "string"},"user_id": {"type": "string"}}, "required": ["field_name"]}}
        tools_dict["check_earliest_irrigation_dates"] = {"function": check_earliest_irrigation_dates, "description": "Check the earliest irrigation dates for all fields", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["check_highest_evapotranspiration"] = {"function": check_highest_evapotranspiration, "description": "Check which fields have the highest evapotranspiration", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["check_future_irrigation_fields"] = {"function": check_future_irrigation_fields, "description": "Check which fields will need irrigation in the future", "parameters": {"type": "object","properties": {"user_id": {"type": "string"}}, "required": []}}
        tools_dict["predicted_water_consumption_rate_for_x_days"] = {"function": predicted_water_consumption_rate_for_x_days, "description": "Get predicted water consumption rate for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}
        tools_dict["total_water_consumption_predicted_for_each_field_x_days"] = {"function": total_water_consumption_predicted_for_each_field_x_days, "description": "Get total water consumption predicted for each field for the next X days", "parameters": {"type": "object","properties": {"days": {"type": "integer"},"user_id": {"type": "string"}}, "required": ["days"]}}

        print(f"Registered {len(tools_dict)} tools for Mistral AI direct")
        return tools_dict

    def _format_tools_for_mistral(self) -> List[Dict[str, Any]]:
        """Format tools for Mistral HTTP API (function declarations)."""
        mistral_tools: List[Dict[str, Any]] = []
        for tool_name, tool_info in self.tools.items():
            mistral_tools.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool_info["description"],
                    "parameters": tool_info["parameters"]
                }
            })
        return mistral_tools

    def _execute_tool(self, tool_name: str, tool_args: Dict[str, Any], user_id: str = None, target_language: str = "english") -> Any:
        """Execute a local tool and translate the result when needed (same logic as GPT4oDirectTools)."""
        print(f"Executing tool: {tool_name} with args: {tool_args}")

        if tool_name not in self.tools:
            error_msg = f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"
            return self._translate_tool_response(error_msg, target_language)

        # Block memory tools if present
        if tool_name in ["add_memory", "get_memories", "delete_memory", "clear_memories"]:
            print(f"⛔ MEMORY TOOL BLOCKED: Refusing to execute memory tool '{tool_name}'")
            result = "Memory functionality is disabled. I rely on the conversation history for context."
            return self._translate_tool_response(str(result), target_language)

        tool_function = self.tools[tool_name]["function"]
        execution_args = tool_args.copy() if isinstance(tool_args, dict) else {}

        try:
            # Decide how to call the function by inspecting its signature to avoid unexpected kwargs
            # Resolve actual callable (support langchain-style wrappers with .func)
            callable_fn = getattr(tool_function, "func", tool_function)

            # Determine whether to call with kwargs or as a single dict/positional args
            try:
                sig = inspect.signature(callable_fn)
                params = [p for p in sig.parameters.values() if p.name != "self"]
                accepts_var_kw = any(p.kind == inspect.Parameter.VAR_KEYWORD for p in params)
                accepts_user_id_kw = "user_id" in sig.parameters
            except Exception:
                # If signature inspection fails, conservatively allow kwargs
                accepts_var_kw = True
                accepts_user_id_kw = True

            # Add user_id where appropriate:
            tool_params = self.tools[tool_name].get("parameters", {}).get("properties", {})
            if "user_id" in tool_params and user_id:
                if accepts_var_kw or accepts_user_id_kw:
                    # safe to add as kwarg
                    execution_args["user_id"] = user_id
                else:
                    # function probably expects a single dict arg -> ensure user_id present inside dict
                    if isinstance(execution_args, dict):
                        execution_args["user_id"] = user_id

            # Special cache-busting logic for irrigation tools to ensure fresh data
            irrigation_tools = [
                "check_irrigation_user_data", "check_today_active_irrigation_user", "get_lowest_soil_water_volume",
                "calculate_total_irrigation_volume_next_x_days", "check_irrigation_need_for_x_days", "advise_stop_over_irrigation",
                "check_highest_irrigation_requirement", "fields_exceeding_water_capacity_for_x_days",
                "fields_with_highest_evapotranspiration_next_x_days", "fields_with_highest_water_requirements_for_x_days",
                "fields_with_optimal_soil_moisture_for_x_days", "find_fields_no_irrigation_needs_for_x_days",
                "fields_predicted_to_exceed_water_capacity_for_x_days", "get_current_irrigation_status",
                "check_irrigation_needs_between_period", "check_soil_water_volume", "check_earliest_irrigation_dates",
                "check_highest_evapotranspiration", "check_future_irrigation_fields",
                "predicted_water_consumption_rate_for_x_days", "total_water_consumption_predicted_for_each_field_x_days"
            ]
            if tool_name in irrigation_tools:
                execution_args["_cache_buster"] = datetime.now().isoformat()
                # Remove internal _cache_buster before calling actual function
                clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}
            else:
                clean_args = execution_args

            # Execute the tool function using the safest calling pattern determined above
            if hasattr(tool_function, "func"):
                callable_fn = tool_function.func
            else:
                callable_fn = tool_function

            # If callable accepts kwargs (or we detected multiple params), prefer kwargs call
            if accepts_var_kw or accepts_user_id_kw or len([p for p in sig.parameters.values() if p.name != "self"]) > 1:
                result = callable_fn(**clean_args) if clean_args else callable_fn()
            else:
                # Likely a single-argument function -> pass the dict as the single positional arg
                try:
                    result = callable_fn(**clean_args) if clean_args else callable_fn()
                except TypeError:
                    result = callable_fn(clean_args) if isinstance(clean_args, dict) else callable_fn()

            translated_result = self._translate_tool_response(str(result), target_language)
            print(f"Tool execution result: {translated_result}")
            return translated_result
        except Exception as e:
            traceback.print_exc()
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            return self._translate_tool_response(error_msg, target_language)

    def _detect_language(self, text: str) -> str:
        """Simple heuristic language detection (same as other files)."""
        try:
            text_lower = text.lower()
            if any(word in text_lower for word in ['combien', 'est-ce', 'que', 'champs', 'irrigation']):
                return "french"
            if any(word in text_lower for word in ['cuánto', 'riego', 'campo', 'necesito']):
                return "spanish"
            if any(word in text_lower for word in ['ماء', 'حقل', 'ري', 'اليوم']):
                return "tunisian_arabic"
            return "english"
        except Exception as e:
            print(f"Error in language detection: {e}")
            return "english"

    def _translate_tool_response(self, tool_response: str, target_language: str) -> str:
        """Translate a tool response using the Mistral model when translation is enabled."""
        if not self.enable_translation or target_language == "english" or not tool_response or not tool_response.strip():
            return tool_response

        lang_map = {"french": "French", "spanish": "Spanish", "tunisian_arabic": "Tunisian Arabic"}
        target_lang_name = lang_map.get(target_language)
        if not target_lang_name:
            return tool_response

        translation_prompt = f"Translate the following agricultural tool response to {target_lang_name}. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:\n\n{tool_response}\n\nProvide only the translation, no explanations."

        try:
            # Use the same Vertex endpoint to perform translation (single user message)
            messages = [{"role": "system", "content": "You are a professional translator specializing in agricultural terminology."},
                        {"role": "user", "content": translation_prompt}]
            response_json = call_mistral_api(messages, temperature=0.1, max_tokens=1000, tools=None, tool_choice="none", timeout=30.0)
            translated_text = response_json.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
            if translated_text:
                print(f"Translated tool response to {target_language}: {translated_text}")
                return translated_text
            else:
                print("Translation returned empty content, returning original tool response.")
                return tool_response
        except Exception as e:
            print(f"Error translating tool response: {e}")
            return tool_response

    def ask(self, question: str, chat_history: str = "", user_id: str = None) -> str:
        """Process a question and return a response using Mistral AI with tool calling (two-step flow)."""
        try:
            detected_language = self._detect_language(question)
            print(f"Detected language: {detected_language}")

            # Build messages list
            messages = [{"role": "system", "content": SYSTEM_PROMPT}]
            if chat_history:
                try:
                    history_list = json.loads(chat_history)
                    messages.extend(history_list)
                except json.JSONDecodeError:
                    print("Warning: Could not parse chat_history JSON. Starting fresh.")

            messages.append({"role": "user", "content": question})

            tools = self._format_tools_for_mistral()
            print(f"Sending request to Mistral AI with {len(tools)} tools available")

            # First call: ask model what to do (it may request tool calls)
            try:
                response_json = call_mistral_api(messages, temperature=0.7, max_tokens=1000, tools=tools, tool_choice="auto", timeout=None)
            except Exception as e:
                # If Vertex errors when passing tools, retry once without tools
                print(f"Initial call failed with tools: {e}. Retrying without tools.")
                response_json = call_mistral_api(messages, temperature=0.7, max_tokens=1000, tools=None, tool_choice="none", timeout=None)

            choice = response_json.get("choices", [{}])[0]
            response_message = choice.get("message", {})

            # If model requested tool_calls, execute them and send results back
            if response_message.get("tool_calls"):
                tool_calls = response_message["tool_calls"]
                # Append assistant message that contains tool call decision
                messages.append(response_message)

                tool_outputs: List[str] = []

                for tool_call in tool_calls:
                    tool_name = tool_call["function"]["name"]
                    tool_args_str = tool_call["function"].get("arguments", "{}")
                    # some responses may already provide a dict
                    try:
                        tool_args = json.loads(tool_args_str) if isinstance(tool_args_str, str) else tool_args_str
                    except Exception:
                        tool_args = {}

                    print(f"Mistral AI requested tool: {tool_name} args: {tool_args}")
                    tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                    # Append tool result as a tool message
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.get("id"),
                        "name": tool_name,
                        "content": str(tool_result)
                    })

                    tool_outputs.append(str(tool_result))

                # Prefer returning executed tool output directly (avoids Vertex role-ordering issues).
                # If single tool called and it returned a user-facing message, return it.
                def _looks_like_user_facing(text: str) -> bool:
                    if not text or not text.strip():
                        return False
                    low = text.strip().lower()
                    # heuristics: actionable phrasing or irrigation units -> user-facing
                    if low.startswith("you need") or "mm" in low or "no irrigation" in low or "no data" in low:
                        return True
                    # if text is a friendly sentence paragraph, treat as user-facing
                    if len(low.split()) > 3 and "." in text:
                        return True
                    return False

                if len(tool_outputs) == 1 and _looks_like_user_facing(tool_outputs[0]):
                    final_answer = tool_outputs[0]
                    print(f"Returning single-tool result directly as final answer: {final_answer}")
                else:
                    # Multiple tool outputs or uncertain content: synthesize locally into a clear user response.
                    # Present each tool output prefixed so the user can read them in order.
                    synthesized_lines = []
                    for idx, out in enumerate(tool_outputs):
                        synthesized_lines.append(f"{out.strip()}")
                    final_answer = "\n\n".join(synthesized_lines) if synthesized_lines else "I couldn't obtain tool results to produce an answer."
                    print("Returning synthesized tool outputs as final answer to avoid extra model call.")
            else:
                # No tool calls, return the direct assistant response
                final_answer = response_message.get("content", "").strip()

            print(f"Final response: {final_answer}")
            return final_answer

        except Exception as e:
            traceback.print_exc()
            return f"I apologize, but I encountered an error. Error: {str(e)}. Please try again."

# Create a singleton instance (mirrors gpt4o_direct naming pattern but mistral)
mistral_direct = MistralDirectTools()

# If run as script, run a small smoke test (keeps parity with other direct files)
def test_mistral_direct():
    mistral_tools = MistralDirectTools()
    test_questions = [
        "do i need to irrigate today?",
        "what fields need irrigation today?",
        "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?",
        "show me all my parcels irrigation status"
    ]
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    for q in test_questions:
        print("="*40)
        print("Q:", q)
        print("A:", mistral_tools.ask(q, user_id=user_id))

if __name__ == "__main__":
    test_mistral_direct()