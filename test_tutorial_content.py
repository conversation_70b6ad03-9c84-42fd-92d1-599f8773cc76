#!/usr/bin/env python3
"""
Test script to verify tutorial content without Redis dependency
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_tutorial_content():
    """Test if the tutorial content contains information about adding areas."""
    print("🔍 Testing Tutorial Content...")
    
    # Check if tutorial file exists
    tutorial_file = "tutorial-ENG-enhanced.txt"
    if not os.path.exists(tutorial_file):
        print(f"❌ Tutorial file not found: {tutorial_file}")
        return False
    
    print(f"✅ Found tutorial file: {tutorial_file}")
    
    # Read and analyze content
    with open(tutorial_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"📄 Tutorial file size: {len(content)} characters")
    
    # Check for relevant keywords
    keywords_to_check = [
        'add a new area',
        'create a new field',
        'add area',
        'field creation',
        'boundaries',
        'trace',
        'GPS'
    ]
    
    found_keywords = []
    for keyword in keywords_to_check:
        if keyword.lower() in content.lower():
            found_keywords.append(keyword)
    
    print(f"✅ Found {len(found_keywords)} relevant keywords: {found_keywords}")
    
    # Split content into Q&A pairs
    sections = content.split("*" * 50)
    qa_pairs = []
    
    for section in sections:
        section = section.strip()
        if section and "Question:" in section and "Answer:" in section:
            lines = section.split('\n')
            question = ""
            answer = ""
            
            for line in lines:
                if line.startswith("Question:"):
                    question = line.replace("Question:", "").strip()
                elif line.startswith("Answer:"):
                    answer = line.replace("Answer:", "").strip()
                elif answer and line.strip():  # Continue answer on next lines
                    answer += " " + line.strip()
            
            if question and answer:
                qa_pairs.append((question, answer))
    
    print(f"📋 Found {len(qa_pairs)} Q&A pairs")
    
    # Test specific query
    test_query = "how to add a new area"
    print(f"\n🔍 Testing query: '{test_query}'")
    
    # Simple similarity matching
    best_match = None
    best_score = 0
    
    for question, answer in qa_pairs:
        # Simple keyword matching score
        query_words = test_query.lower().split()
        question_words = question.lower().split()
        
        matches = sum(1 for word in query_words if word in question_words)
        score = matches / len(query_words)
        
        if score > best_score:
            best_score = score
            best_match = (question, answer)
    
    if best_match:
        question, answer = best_match
        print(f"✅ Best match (score: {best_score:.2f}):")
        print(f"   Question: {question}")
        print(f"   Answer: {answer[:200]}...")
        return True
    else:
        print("❌ No good matches found")
        return False

def test_simple_rag_simulation():
    """Simulate RAG functionality without Redis."""
    print("\n🧪 Simulating RAG Functionality...")
    
    try:
        # Read tutorial content
        with open("tutorial-ENG-enhanced.txt", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse Q&A pairs
        sections = content.split("*" * 50)
        qa_pairs = []
        
        for section in sections:
            section = section.strip()
            if section and "Question:" in section and "Answer:" in section:
                lines = section.split('\n')
                question = ""
                answer_lines = []
                in_answer = False
                
                for line in lines:
                    if line.startswith("Question:"):
                        question = line.replace("Question:", "").strip()
                        in_answer = False
                    elif line.startswith("Answer:"):
                        answer_lines.append(line.replace("Answer:", "").strip())
                        in_answer = True
                    elif in_answer and line.strip():
                        answer_lines.append(line.strip())
                
                if question and answer_lines:
                    answer = " ".join(answer_lines)
                    qa_pairs.append((question, answer))
        
        print(f"📚 Loaded {len(qa_pairs)} Q&A pairs")
        
        # Test queries
        test_queries = [
            "how to add a new area?",
            "how to create a new field?",
            "where is the add area button?",
            "how to trace boundaries?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Query: {query}")
            
            # Find best matching Q&A
            best_match = None
            best_score = 0
            
            query_words = set(query.lower().replace('?', '').split())
            
            for question, answer in qa_pairs:
                question_words = set(question.lower().replace('?', '').split())
                
                # Calculate Jaccard similarity
                intersection = len(query_words.intersection(question_words))
                union = len(query_words.union(question_words))
                score = intersection / union if union > 0 else 0
                
                if score > best_score:
                    best_score = score
                    best_match = (question, answer)
            
            if best_match and best_score > 0.1:
                question, answer = best_match
                print(f"✅ Match found (score: {best_score:.3f})")
                print(f"📱 **Agrisense Mobile App Guide:**")
                print(f"📋 **Instructions:** {answer[:300]}...")
                print(f"💡 **Tip:** Follow the steps in order and use the app's help features.")
            else:
                print("❌ No suitable match found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in RAG simulation: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Tutorial Content Test")
    print("=" * 40)
    
    # Test tutorial content
    content_success = test_tutorial_content()
    
    # Test RAG simulation
    rag_success = test_simple_rag_simulation()
    
    # Summary
    print("\n📋 Test Results")
    print("=" * 20)
    print(f"Tutorial Content: {'✅ Success' if content_success else '❌ Failed'}")
    print(f"RAG Simulation: {'✅ Success' if rag_success else '❌ Failed'}")
    
    if content_success and rag_success:
        print("\n🎉 Tutorial content is working!")
        print("\n📝 The system should now be able to answer questions about:")
        print("- Adding new areas/fields")
        print("- Creating field boundaries")
        print("- Navigating the app interface")
        print("- Field management procedures")
        
        print("\n🔧 Next Steps:")
        print("1. Fix Redis connection for full RAG functionality")
        print("2. Test with the actual magonia system")
        print("3. Process the full PDF for complete coverage")
    else:
        print("\n⚠️ Some tests failed. Check the tutorial content.")

if __name__ == "__main__":
    main()
