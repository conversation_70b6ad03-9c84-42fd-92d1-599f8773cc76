import fitz  # PyMuPDF
import base64
import os
from PIL import Image
import io
from openai import OpenAI
import json
from typing import List, Dict, Any
import re

class MultimodalTutorialProcessor:
    def __init__(self):
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        self.content_blocks = []
        self.processed_images = []
    
    def extract_pdf_content(self, pdf_path):
        """Extract both text and images from PDF with context."""
        doc = fitz.open(pdf_path)
        content_blocks = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Extract text
            text = page.get_text()
            if text.strip():
                content_blocks.append({
                    'type': 'text',
                    'page': page_num + 1,
                    'content': text.strip()
                })
            
            # Extract images
            image_list = page.get_images()
            for img_index, img in enumerate(image_list):
                try:
                    # Get image data
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        
                        # Analyze image with GPT-4V, including surrounding text as context
                        image_description = self.analyze_image_with_gpt4v(img_data, page_num + 1, text.strip())
                        
                        content_blocks.append({
                            'type': 'image',
                            'page': page_num + 1,
                            'image_index': img_index,
                            'description': image_description,
                            'context': text.strip()  # Include surrounding text as context
                        })
                    
                    pix = None
                except Exception as e:
                    print(f"Error processing image on page {page_num + 1}: {e}")
        
        doc.close()
        return content_blocks
    
    def analyze_image_with_gpt4v(self, image_data, page_num, context_text=""):
        """Analyze image using GPT-4V to extract detailed UI elements and instructions."""
        try:
            # Convert to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')

            # Enhanced prompt for better UI analysis
            analysis_prompt = f"""Analyze this mobile app screenshot from the Agrisense agricultural platform tutorial. Provide a comprehensive analysis:

**INTERFACE IDENTIFICATION:**
- Screen type (login, dashboard, field management, irrigation control, settings, etc.)
- Main purpose of this screen

**UI ELEMENTS ANALYSIS:**
- Buttons: List all visible buttons with their exact text/labels and functions
- Input fields: Describe form fields, text inputs, dropdowns, toggles
- Navigation: Menu items, tabs, back buttons, breadcrumbs
- Icons: Describe icons and their meanings
- Data displays: Charts, lists, cards, status indicators

**ACTIONABLE INSTRUCTIONS:**
- What specific actions can users take on this screen?
- What should users click/tap to proceed?
- What information do users need to enter?
- What are the next steps in the workflow?

**VISUAL LAYOUT:**
- Header/title area content
- Main content area organization
- Footer/bottom navigation
- Color coding or visual indicators

**TEXT CONTENT:**
- All visible text, labels, headings, and messages
- Error messages or notifications if any

**CONTEXT INTEGRATION:**
{f"Related text context: {context_text[:200]}..." if context_text else "No additional text context available."}

Format your response to be helpful for users learning to navigate the app."""

            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": analysis_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=800,
                temperature=0.1
            )

            return response.choices[0].message.content

        except Exception as e:
            print(f"Error analyzing image on page {page_num}: {e}")
            return f"Image from page {page_num} - Unable to analyze content due to: {str(e)}"
    
    def create_structured_tutorial(self, content_blocks, output_path):
        """Create a structured text file with both text and image descriptions."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Agrisense Platform Tutorial - Complete Guide\n\n")
            
            current_page = 1
            for block in content_blocks:
                if block['page'] != current_page:
                    current_page = block['page']
                    f.write(f"\n## Page {current_page}\n\n")
                
                if block['type'] == 'text':
                    f.write(f"{block['content']}\n\n")
                
                elif block['type'] == 'image':
                    f.write(f"### Screenshot Analysis (Image {block['image_index'] + 1})\n")
                    f.write(f"{block['description']}\n\n")
                    if block['context']:
                        f.write(f"**Context:** {block['context'][:200]}...\n\n")
                
                f.write("*" * 50 + "\n\n")

    def create_enhanced_embeddings(self, content_blocks, output_path):
        """Create enhanced structured content optimized for RAG retrieval."""
        structured_content = []

        for i, block in enumerate(content_blocks):
            if block['type'] == 'text':
                # Process text blocks for better searchability
                text_content = block['content']

                # Create searchable text entry
                structured_entry = {
                    'id': f"text_{block['page']}_{i}",
                    'type': 'instruction',
                    'page': block['page'],
                    'content': text_content,
                    'searchable_text': self._extract_keywords(text_content),
                    'content_type': 'text_instruction'
                }
                structured_content.append(structured_entry)

            elif block['type'] == 'image':
                # Process image analysis for UI guidance
                image_analysis = block['description']

                # Create UI guidance entry
                ui_entry = {
                    'id': f"ui_{block['page']}_{block['image_index']}",
                    'type': 'ui_guidance',
                    'page': block['page'],
                    'content': image_analysis,
                    'context': block.get('context', ''),
                    'searchable_text': self._extract_ui_keywords(image_analysis),
                    'content_type': 'screenshot_analysis'
                }
                structured_content.append(ui_entry)

        # Save structured content for embedding
        with open(output_path, 'w', encoding='utf-8') as f:
            for entry in structured_content:
                # Format for vector store consumption
                if entry['type'] == 'ui_guidance':
                    f.write(f"Question: How do I use the {entry['searchable_text']} in the app?\n")
                    f.write(f"Answer: {entry['content']}\n")
                    if entry['context']:
                        f.write(f"Context: {entry['context'][:200]}...\n")
                else:
                    f.write(f"Question: {entry['searchable_text']}\n")
                    f.write(f"Answer: {entry['content']}\n")

                f.write("*" * 50 + "\n\n")

        return structured_content

    def _extract_keywords(self, text):
        """Extract relevant keywords from text for better searchability."""
        # Simple keyword extraction - can be enhanced with NLP
        keywords = []

        # Common agricultural/app terms to look for
        key_terms = [
            'field', 'irrigation', 'water', 'schedule', 'dashboard', 'login', 'settings',
            'create', 'add', 'edit', 'delete', 'view', 'manage', 'control', 'monitor',
            'weather', 'soil', 'moisture', 'sensor', 'data', 'report', 'history',
            'notification', 'alert', 'status', 'zone', 'valve', 'pump', 'system'
        ]

        text_lower = text.lower()
        for term in key_terms:
            if term in text_lower:
                keywords.append(term)

        # Extract questions or action phrases
        if '?' in text:
            keywords.append('question')
        if any(word in text_lower for word in ['how to', 'step', 'click', 'tap', 'press']):
            keywords.append('instructions')

        return ' '.join(keywords) if keywords else text[:100]

    def _extract_ui_keywords(self, image_analysis):
        """Extract UI-specific keywords from image analysis."""
        ui_keywords = []

        # UI element terms
        ui_terms = [
            'button', 'menu', 'icon', 'field', 'form', 'input', 'dropdown', 'toggle',
            'navigation', 'tab', 'screen', 'page', 'dialog', 'popup', 'header', 'footer',
            'dashboard', 'login', 'settings', 'profile', 'search', 'filter', 'sort',
            'map', 'chart', 'graph', 'list', 'card', 'panel', 'sidebar'
        ]

        analysis_lower = image_analysis.lower()
        for term in ui_terms:
            if term in analysis_lower:
                ui_keywords.append(term)

        # Extract specific UI actions
        if any(word in analysis_lower for word in ['click', 'tap', 'press', 'select']):
            ui_keywords.append('user_action')
        if any(word in analysis_lower for word in ['enter', 'input', 'type', 'fill']):
            ui_keywords.append('data_entry')

        return ' '.join(ui_keywords) if ui_keywords else 'mobile app interface'

if __name__ == "__main__":
    processor = MultimodalTutorialProcessor()
    content_blocks = processor.extract_pdf_content("Agrisense plateform tutorial-ENG.pdf")

    # Create both formats
    processor.create_structured_tutorial(content_blocks, "tutorial-ENG-multimodal.txt")
    processor.create_enhanced_embeddings(content_blocks, "tutorial-ENG-enhanced.txt")

    print("Multimodal tutorial processing complete!")
    print("Created files:")
    print("- tutorial-ENG-multimodal.txt (detailed format)")
    print("- tutorial-ENG-enhanced.txt (optimized for RAG)")