import fitz  # PyMuPDF
import base64
import os
from PIL import Image
import io
from openai import OpenAI

class MultimodalTutorialProcessor:
    def __init__(self):
        self.openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    def extract_pdf_content(self, pdf_path):
        """Extract both text and images from PDF with context."""
        doc = fitz.open(pdf_path)
        content_blocks = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Extract text
            text = page.get_text()
            if text.strip():
                content_blocks.append({
                    'type': 'text',
                    'page': page_num + 1,
                    'content': text.strip()
                })
            
            # Extract images
            image_list = page.get_images()
            for img_index, img in enumerate(image_list):
                try:
                    # Get image data
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        
                        # Analyze image with GPT-4V
                        image_description = self.analyze_image_with_gpt4v(img_data, page_num + 1)
                        
                        content_blocks.append({
                            'type': 'image',
                            'page': page_num + 1,
                            'image_index': img_index,
                            'description': image_description,
                            'context': text.strip()  # Include surrounding text as context
                        })
                    
                    pix = None
                except Exception as e:
                    print(f"Error processing image on page {page_num + 1}: {e}")
        
        doc.close()
        return content_blocks
    
    def analyze_image_with_gpt4v(self, image_data, page_num):
        """Analyze image using GPT-4V to extract UI elements and instructions."""
        try:
            # Convert to base64
            base64_image = base64.b64encode(image_data).decode('utf-8')
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": """Analyze this mobile app screenshot from the Agrisense platform tutorial. Describe:
1. What screen/interface is shown
2. Key UI elements (buttons, menus, fields, icons)
3. Step-by-step actions visible or implied
4. Any text or labels visible in the interface
5. Navigation elements or workflow steps
6. Data or information displayed

Focus on actionable information that would help users understand how to use the app."""
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500,
                temperature=0.1
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"Error analyzing image: {e}")
            return f"Image from page {page_num} - Unable to analyze content"
    
    def create_structured_tutorial(self, content_blocks, output_path):
        """Create a structured text file with both text and image descriptions."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Agrisense Platform Tutorial - Complete Guide\n\n")
            
            current_page = 1
            for block in content_blocks:
                if block['page'] != current_page:
                    current_page = block['page']
                    f.write(f"\n## Page {current_page}\n\n")
                
                if block['type'] == 'text':
                    f.write(f"{block['content']}\n\n")
                
                elif block['type'] == 'image':
                    f.write(f"### Screenshot Analysis (Image {block['image_index'] + 1})\n")
                    f.write(f"{block['description']}\n\n")
                    if block['context']:
                        f.write(f"**Context:** {block['context'][:200]}...\n\n")
                
                f.write("*" * 50 + "\n\n")

if __name__ == "__main__":
    processor = MultimodalTutorialProcessor()
    content_blocks = processor.extract_pdf_content("Agrisense plateform tutorial-ENG.pdf")
    processor.create_structured_tutorial(content_blocks, "tutorial-ENG-multimodal.txt")
    print("Multimodal tutorial processing complete!")