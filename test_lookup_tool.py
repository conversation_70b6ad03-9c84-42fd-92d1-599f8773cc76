#!/usr/bin/env python3
"""
Test the lookup tool with the enhanced tutorial content
"""

import sys
import os
sys.path.append('.')

def test_lookup_tool():
    """Test the lookup tool functionality."""
    print("Testing lookup tool with enhanced tutorial content...")
    
    try:
        from magonia.tools.lookup_document_tool import lookup_document_tool
        
        # Test queries
        test_queries = [
            "how to add a new area?",
            "how to create a new field?",
            "where is the add area button?",
            "how to trace boundaries?"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testing query: {query}")
            try:
                result = lookup_document_tool(query)
                print("✅ Lookup successful!")
                print(f"📱 Result: {result}")
                print("-" * 50)
            except Exception as e:
                print(f"❌ Error for query '{query}': {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ General error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_lookup_tool()
