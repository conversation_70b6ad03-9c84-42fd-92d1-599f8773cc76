import os
from langchain_openai import OpenAIEmbeddings
from langchain_redis import RedisConfig, RedisVectorStore
from typing import List, Dict, Any
import json

class EnhancedVectorStoreRetriever:
    """Enhanced vector store retriever optimized for multimodal tutorial content."""
    
    def __init__(self, redis_url, index_name):
        if not redis_url:
            raise ValueError("The REDIS_URL environment variable is not set or is invalid.")

        self.embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        self.config = RedisConfig(
            index_name=index_name,
            redis_url=redis_url,
            metadata_schema=[
                {"name": "category", "type": "tag"},
                {"name": "content_type", "type": "tag"},
                {"name": "page", "type": "numeric"},
                {"name": "ui_element", "type": "tag"}
            ],
        )
        self.vector_store = RedisVectorStore(self.embeddings, config=self.config)

    def load_enhanced_tutorial(self, file_path):
        """Load enhanced tutorial content with improved metadata."""
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Split the content by the delimiter
        entries = content.split("*" * 50)
        texts = []
        metadatas = []

        for entry in entries:
            entry = entry.strip()
            if entry:
                # Parse enhanced format
                lines = entry.split('\n')
                question = ""
                answer = ""
                context = ""
                
                for line in lines:
                    if line.startswith("Question:"):
                        question = line.replace("Question:", "").strip()
                    elif line.startswith("Answer:"):
                        answer = line.replace("Answer:", "").strip()
                    elif line.startswith("Context:"):
                        context = line.replace("Context:", "").strip()
                
                if question and answer:
                    # Combine question and answer for embedding
                    full_text = f"{question} {answer}"
                    texts.append(full_text)
                    
                    # Enhanced metadata
                    metadata = {
                        "category": "tutorial",
                        "content_type": self._detect_content_type(question, answer),
                        "ui_element": self._extract_ui_elements(answer),
                        "page": self._extract_page_number(answer)
                    }
                    metadatas.append(metadata)

        if not texts:
            raise ValueError("No valid tutorial content found in the file.")

        ids = self.vector_store.add_texts(texts, metadatas)
        return ids

    def _detect_content_type(self, question, answer):
        """Detect the type of content based on question and answer."""
        question_lower = question.lower()
        answer_lower = answer.lower()
        
        # UI/Interface related
        if any(term in question_lower for term in ['interface', 'screen', 'button', 'menu', 'icon']):
            return "ui_guidance"
        if any(term in answer_lower for term in ['click', 'tap', 'press', 'select', 'navigate']):
            return "ui_guidance"
            
        # Step-by-step instructions
        if any(term in question_lower for term in ['how to', 'how do i', 'steps']):
            return "instructions"
        if any(term in answer_lower for term in ['step 1', 'first', 'then', 'next', 'finally']):
            return "instructions"
            
        # Feature explanation
        if any(term in question_lower for term in ['what is', 'what does', 'explain']):
            return "explanation"
            
        return "general"

    def _extract_ui_elements(self, text):
        """Extract UI elements mentioned in the text."""
        ui_elements = []
        text_lower = text.lower()
        
        ui_terms = [
            'dashboard', 'login', 'menu', 'button', 'field', 'form', 'dropdown',
            'navigation', 'tab', 'icon', 'map', 'chart', 'list', 'settings',
            'profile', 'search', 'filter', 'irrigation', 'weather', 'sensor'
        ]
        
        for term in ui_terms:
            if term in text_lower:
                ui_elements.append(term)
        
        return ','.join(ui_elements) if ui_elements else 'general'

    def _extract_page_number(self, text):
        """Extract page number if mentioned in the text."""
        import re
        page_match = re.search(r'page (\d+)', text.lower())
        return int(page_match.group(1)) if page_match else 0

    def smart_lookup(self, query: str, k: int = 3, content_type_filter: str = None):
        """Enhanced lookup with smart filtering and ranking."""
        # Determine query intent
        query_lower = query.lower()
        
        # Build filter based on query intent
        filter_expr = None
        if content_type_filter:
            filter_expr = f"@content_type:{content_type_filter}"
        elif any(term in query_lower for term in ['how to', 'how do i', 'steps']):
            filter_expr = "@content_type:instructions"
        elif any(term in query_lower for term in ['what is', 'what does', 'screen', 'interface']):
            filter_expr = "@content_type:(ui_guidance|explanation)"
        elif any(term in query_lower for term in ['button', 'menu', 'click', 'tap', 'navigate']):
            filter_expr = "@content_type:ui_guidance"

        # Perform search with optional filtering
        if filter_expr:
            try:
                results = self.vector_store.similarity_search(
                    query, 
                    k=k,
                    filter=filter_expr
                )
            except:
                # Fallback to regular search if filtering fails
                results = self.vector_store.similarity_search(query, k=k)
        else:
            results = self.vector_store.similarity_search(query, k=k)

        return results

    def lookup_document(self, query: str, k: int = 2):
        """Standard lookup method for compatibility."""
        return self.smart_lookup(query, k)

    def get_ui_guidance(self, query: str, k: int = 2):
        """Specialized method for UI guidance queries."""
        return self.smart_lookup(query, k, "ui_guidance")

    def get_instructions(self, query: str, k: int = 3):
        """Specialized method for step-by-step instructions."""
        return self.smart_lookup(query, k, "instructions")

    def load_and_embed(self, file_path):
        """Legacy method for backward compatibility."""
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Split the content by the delimiter
        entries = content.split("*********")
        texts = []

        for entry in entries:
            entry = entry.strip()
            if entry:
                parts = entry.split("Réponse:")
                if len(parts) == 2:
                    question = parts[0].replace("Question:", "").strip()
                    answer = parts[1].strip()
                    texts.append(f"{question} {answer}")

        if not texts:
            raise ValueError("No valid question-answer pairs found in the file.")

        metadata = [{"category": "general"}] * len(texts)
        ids = self.vector_store.add_texts(texts, metadata)
        return ids
