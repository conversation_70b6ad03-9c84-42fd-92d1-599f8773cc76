#!/usr/bin/env python3
"""
Simple test script to verify PDF processing functionality
"""

import os
import sys
from dotenv import load_dotenv

# Add the current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def test_pdf_processing():
    """Test basic PDF processing functionality."""
    print("🔄 Testing PDF Processing...")
    
    # Check if required files exist
    pdf_path = "Agrisense plateform tutorial-ENG.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return False
    
    print(f"✅ Found PDF: {pdf_path}")
    
    # Check if OpenAI API key is set
    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key:
        print("❌ OPENAI_API_KEY not set in environment")
        return False
    
    print("✅ OpenAI API key found")
    
    try:
        # Import and test the processor
        from scripts.process_tutorial_multimodal import MultimodalTutorialProcessor
        
        print("✅ Successfully imported MultimodalTutorialProcessor")
        
        # Initialize processor
        processor = MultimodalTutorialProcessor()
        print("✅ Processor initialized")
        
        # Test with just the first page to avoid long processing
        print("🔄 Processing first few pages of PDF...")
        
        import fitz  # PyMuPDF
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        print(f"📄 PDF has {total_pages} pages")
        
        # Process just first 2 pages for testing
        test_pages = min(2, total_pages)
        print(f"🧪 Testing with first {test_pages} pages...")
        
        content_blocks = []
        for page_num in range(test_pages):
            page = doc[page_num]
            
            # Extract text
            text = page.get_text()
            if text.strip():
                content_blocks.append({
                    'type': 'text',
                    'page': page_num + 1,
                    'content': text.strip()[:200] + "..." if len(text.strip()) > 200 else text.strip()
                })
                print(f"   ✅ Extracted text from page {page_num + 1}")
            
            # Extract images (limit to 1 per page for testing)
            image_list = page.get_images()
            if image_list:
                print(f"   📷 Found {len(image_list)} images on page {page_num + 1}")
                
                # Process just the first image for testing
                try:
                    img = image_list[0]
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")
                        print(f"   🔄 Analyzing image from page {page_num + 1}...")
                        
                        # Test image analysis
                        image_description = processor.analyze_image_with_gpt4v(
                            img_data, page_num + 1, text.strip()[:100]
                        )
                        
                        content_blocks.append({
                            'type': 'image',
                            'page': page_num + 1,
                            'image_index': 0,
                            'description': image_description,
                            'context': text.strip()[:100]
                        })
                        
                        print(f"   ✅ Successfully analyzed image from page {page_num + 1}")
                        print(f"   📝 Analysis preview: {image_description[:100]}...")
                    
                    pix = None
                    
                except Exception as e:
                    print(f"   ⚠️ Error processing image on page {page_num + 1}: {e}")
        
        doc.close()
        
        print(f"\n✅ Successfully processed {len(content_blocks)} content blocks")
        
        # Test creating structured output
        print("🔄 Testing structured output creation...")
        
        # Create test output files
        processor.create_structured_tutorial(content_blocks, "test-tutorial-multimodal.txt")
        processor.create_enhanced_embeddings(content_blocks, "test-tutorial-enhanced.txt")
        
        print("✅ Created test output files:")
        print("   - test-tutorial-multimodal.txt")
        print("   - test-tutorial-enhanced.txt")
        
        # Show sample content
        if os.path.exists("test-tutorial-enhanced.txt"):
            with open("test-tutorial-enhanced.txt", "r", encoding="utf-8") as f:
                sample_content = f.read()[:500]
            print(f"\n📝 Sample enhanced content:\n{sample_content}...")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required packages are installed:")
        print("   pip install PyMuPDF pillow openai python-dotenv")
        return False
    except Exception as e:
        print(f"❌ Processing error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_store_basic():
    """Test basic vector store functionality."""
    print("\n🔄 Testing Vector Store...")
    
    try:
        from magonia.enhanced_vector_store import EnhancedVectorStoreRetriever
        
        redis_url = os.getenv("REDIS_URL")
        if not redis_url:
            print("⚠️ REDIS_URL not configured, skipping vector store test")
            return True  # Not a failure, just not configured
        
        print("✅ Redis URL found")
        
        # Test creating retriever
        retriever = EnhancedVectorStoreRetriever(redis_url, "test-basic")
        print("✅ Enhanced vector store retriever created")
        
        # Test with sample data if enhanced file exists
        if os.path.exists("test-tutorial-enhanced.txt"):
            print("🔄 Testing with sample data...")
            retriever.load_enhanced_tutorial("test-tutorial-enhanced.txt")
            print("✅ Sample data loaded successfully")
            
            # Test a simple query
            results = retriever.smart_lookup("dashboard interface", k=1)
            print(f"✅ Query test successful, got {len(results)} results")
        else:
            print("⚠️ No test data available, skipping query test")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector store error: {e}")
        return False

def cleanup_test_files():
    """Clean up test files."""
    test_files = ["test-tutorial-multimodal.txt", "test-tutorial-enhanced.txt"]
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ Cleaned up {file}")

def main():
    """Main test function."""
    print("🧪 PDF Processing Test Suite")
    print("=" * 40)
    
    # Test PDF processing
    pdf_success = test_pdf_processing()
    
    # Test vector store
    vector_success = test_vector_store_basic()
    
    # Summary
    print("\n📋 Test Results")
    print("=" * 20)
    print(f"PDF Processing: {'✅ Success' if pdf_success else '❌ Failed'}")
    print(f"Vector Store: {'✅ Success' if vector_success else '❌ Failed'}")
    
    if pdf_success and vector_success:
        print("\n🎉 Basic tests passed!")
        print("\n📝 Ready for full processing. Run:")
        print("   python process_and_test_rag.py")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")
    
    # Ask if user wants to clean up test files
    try:
        cleanup = input("\n🗑️ Clean up test files? (y/n): ").lower().strip()
        if cleanup == 'y':
            cleanup_test_files()
    except KeyboardInterrupt:
        print("\n👋 Test completed.")

if __name__ == "__main__":
    main()
