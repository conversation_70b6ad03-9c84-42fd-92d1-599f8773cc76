# Environment Setup for Testing Irrigation Conversation

## Quick Setup to Run Full Tests

To run the complete `test_irrigation_conversation.py` with actual API calls, you need to set up these environment variables:

### 1. Required Environment Variables

```bash
# OpenAI API Key (required for GPT-4o)
export OPENAI_API_KEY="your-openai-api-key-here"

# Redis URL (required for memory tools)
export REDIS_URL="redis://localhost:6379"
```

### 2. Windows Setup (Command Prompt)
```cmd
set OPENAI_API_KEY=your-openai-api-key-here
set REDIS_URL=redis://localhost:6379
```

### 3. Windows Setup (PowerShell)
```powershell
$env:OPENAI_API_KEY="your-openai-api-key-here"
$env:REDIS_URL="redis://localhost:6379"
```

### 4. Alternative: Create .env File
Create a `.env` file in the project root:
```
OPENAI_API_KEY=your-openai-api-key-here
REDIS_URL=redis://localhost:6379
```

## Running the Tests

### Option 1: Run the Simulation (No Setup Required)
```bash
python test_irrigation_simple.py
```
This shows exactly how the conversation flow works without requiring API keys.

### Option 2: Run Full Test (Requires Setup)
```bash
python test_irrigation_conversation.py
```
This makes actual API calls and demonstrates real conversation flow.

## What the Tests Demonstrate

Both tests show how the enhanced agent handles:

1. **"Do I have to irrigate today?"**
   - Uses `check_today_active_irrigation_user` tool
   - Provides discussion-style response with context

2. **"How much need for field X?"**
   - Uses `check_irrigation_user_data` tool
   - References previous conversation
   - Builds on earlier discussion

3. **Follow-up questions**
   - Maintains conversation context
   - Provides connected, relationship-building responses

## Key Features Demonstrated

✅ **Tool Integration**: Seamless use of appropriate irrigation tools
✅ **Discussion Style**: Natural conversation vs robotic Q&A
✅ **Context Building**: Each response builds on previous exchanges
✅ **Flexibility**: Works with any field names
✅ **Practical Advice**: Actionable farming insights
✅ **Relationship Feel**: Ongoing conversation continuity

## Test Results Summary

The test successfully demonstrates that the enhanced agent can:

- Handle the exact conversation flow you requested
- Provide natural, discussion-style responses
- Build context between questions
- Reference previous conversation elements
- Offer practical farming advice and insights
- Maintain an ongoing relationship feel

The conversation flows naturally from general irrigation questions to specific field inquiries, with each response building on the previous context to create a cohesive, helpful discussion.
