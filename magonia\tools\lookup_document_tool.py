import os
from langchain_core.tools import tool
from magonia.vectore_store_retriever import VectorStoreRetriever
from magonia.enhanced_vector_store import EnhancedVectorStoreRetriever

# Load Redis URL
redis_url = os.getenv("REDIS_URL")

# Create separate retrievers for each document collection
general_retriever = VectorStoreRetriever(redis_url, "general_questions")
tunisian_retriever = VectorStoreRetriever(redis_url, "QuestionTunisian")

# Use enhanced retriever for tutorial content
try:
    enhanced_tutorial_retriever = EnhancedVectorStoreRetriever(redis_url, "tutorial-ENG-enhanced")
except:
    # Fallback to regular retriever if enhanced one fails
    enhanced_tutorial_retriever = VectorStoreRetriever(redis_url, "tutorial-ENG-multimodal")

# Load and embed the documents including enhanced multimodal tutorial
try:
    general_retriever.load_and_embed("general_questions.txt")
    tunisian_retriever.load_and_embed("QuestionTunisian.txt")

    # Try to load enhanced tutorial format first
    if hasattr(enhanced_tutorial_retriever, 'load_enhanced_tutorial'):
        try:
            enhanced_tutorial_retriever.load_enhanced_tutorial("tutorial-ENG-enhanced.txt")
            print(f"Successfully loaded enhanced multimodal tutorial")
        except FileNotFoundError:
            print("Enhanced tutorial file not found, trying to load standard format...")
            if hasattr(enhanced_tutorial_retriever, 'load_tutorial_multimodal'):
                enhanced_tutorial_retriever.load_tutorial_multimodal("tutorial-ENG-multimodal.txt")
            else:
                enhanced_tutorial_retriever.load_and_embed("tutorial-ENG-multimodal.txt")
            print(f"Successfully loaded standard multimodal tutorial")
    else:
        enhanced_tutorial_retriever.load_and_embed("tutorial-ENG-multimodal.txt")
        print(f"Successfully loaded tutorial with basic format")

except Exception as e:
    print(f"Error loading embeddings: {str(e)}")

@tool
def lookup_document_tool(query: str) -> str:
    """
    Search through agricultural knowledge base and multimodal Agrisense tutorial (including UI screenshots).
    
    USE THIS TOOL WHEN:
    - User asks about mobile app interface, navigation, or visual elements
    - User asks "how-to" questions about app operations with step-by-step guidance
    - User needs to understand what buttons, screens, or UI elements to interact with
    - User asks about visual features, icons, or interface layout
    - User wants to know where to find specific functions in the app
    
    EXAMPLE QUERIES:
    - "How do I create a new field in the app?" (will include UI guidance)
    - "What does the irrigation scheduling screen look like?"
    - "Where is the weather data button located?"
    - "How do I navigate to field settings?"
    - "What icons should I look for to add irrigation zones?"
    """
    try:
        # Search tutorial with emphasis on UI/visual content for app questions
        if hasattr(enhanced_tutorial_retriever, 'smart_lookup'):
            tutorial_results = enhanced_tutorial_retriever.smart_lookup(query, k=3)
        else:
            tutorial_results = enhanced_tutorial_retriever.lookup_document(query, k=3)

        general_results = general_retriever.lookup_document(query, k=1)
        tunisian_results = tunisian_retriever.lookup_document(query, k=1)

        # Enhanced keyword detection for better content prioritization
        ui_keywords = ['app', 'interface', 'screen', 'button', 'menu', 'navigation', 'click', 'tap', 'mobile', 'ui', 'dashboard', 'login']
        feature_keywords = ['field', 'irrigation', 'create', 'add', 'edit', 'view', 'manage', 'schedule', 'monitor', 'control']
        action_keywords = ['how to', 'how do i', 'steps', 'guide', 'tutorial', 'instructions', 'where', 'find', 'setup']

        query_lower = query.lower()
        is_ui_query = any(keyword in query_lower for keyword in ui_keywords)
        is_feature_query = any(keyword in query_lower for keyword in feature_keywords)
        is_action_query = any(keyword in query_lower for keyword in action_keywords)

        if tutorial_results and (is_ui_query or is_feature_query or is_action_query):
            # Enhanced content formatting based on query type
            tutorial_content = []

            for i, result in enumerate(tutorial_results[:3]):
                content = result.page_content
                metadata = getattr(result, 'metadata', {})

                # Determine content type and format accordingly
                content_type = metadata.get('content_type', 'general')
                ui_elements = metadata.get('ui_element', '')

                if content_type == 'ui_guidance' or 'screenshot' in content.lower():
                    tutorial_content.append(f"📱 **Visual Guide {i+1}:** {content}")
                elif content_type == 'instructions' or is_action_query:
                    tutorial_content.append(f"📋 **Step-by-Step {i+1}:** {content}")
                else:
                    tutorial_content.append(f"📖 **Information {i+1}:** {content}")

                # Add UI element context if available
                if ui_elements and ui_elements != 'general':
                    tutorial_content.append(f"🎯 **Related UI Elements:** {ui_elements.replace(',', ', ')}")

            combined_content = "\n\n".join(tutorial_content)

            # Add helpful footer based on query type
            footer = ""
            if is_ui_query:
                footer = "\n\n💡 **Tip:** Look for visual indicators and follow the on-screen prompts in the app."
            elif is_action_query:
                footer = "\n\n💡 **Tip:** Follow the steps in order and don't hesitate to use the app's help features."

            return f"**Agrisense Mobile App Guide:**\n\n{combined_content}{footer}"
        
        # Fallback to general knowledge
        all_results = tutorial_results + general_results + tunisian_results
        if all_results:
            return f"Answer: {all_results[0].page_content}"
        
        return "I couldn't find specific information about that in the tutorial or knowledge base."
        
    except Exception as e:
        return f"Error searching tutorial: {str(e)}"
