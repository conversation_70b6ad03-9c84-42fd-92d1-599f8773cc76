import os
from langchain_core.tools import tool
from magonia.vectore_store_retriever import VectorStoreRetriever

# Load Redis URL
redis_url = os.getenv("REDIS_URL")

# Create separate retrievers for each document collection
general_retriever = VectorStoreRetriever(redis_url, "general_questions")
tunisian_retriever = VectorStoreRetriever(redis_url, "QuestionTunisian")
tutorial_retriever = VectorStoreRetriever(redis_url, "tutorial-ENG-multimodal")

# Load and embed the documents including multimodal tutorial
try:
    general_retriever.load_and_embed("general_questions.txt")
    tunisian_retriever.load_and_embed("QuestionTunisian.txt")
    tutorial_retriever.load_tutorial_multimodal("tutorial-ENG-multimodal.txt")
    print(f"Successfully loaded multimodal tutorial with image analysis")
except Exception as e:
    print(f"Error loading embeddings: {str(e)}")

@tool
def lookup_document_tool(query: str) -> str:
    """
    Search through agricultural knowledge base and multimodal Agrisense tutorial (including UI screenshots).
    
    USE THIS TOOL WHEN:
    - User asks about mobile app interface, navigation, or visual elements
    - User asks "how-to" questions about app operations with step-by-step guidance
    - User needs to understand what buttons, screens, or UI elements to interact with
    - User asks about visual features, icons, or interface layout
    - User wants to know where to find specific functions in the app
    
    EXAMPLE QUERIES:
    - "How do I create a new field in the app?" (will include UI guidance)
    - "What does the irrigation scheduling screen look like?"
    - "Where is the weather data button located?"
    - "How do I navigate to field settings?"
    - "What icons should I look for to add irrigation zones?"
    """
    try:
        # Search tutorial with emphasis on UI/visual content for app questions
        tutorial_results = tutorial_retriever.lookup_document(query, k=3)
        general_results = general_retriever.lookup_document(query, k=1)
        tunisian_results = tunisian_retriever.lookup_document(query, k=1)

        # Prioritize tutorial results for app-related questions
        app_keywords = ['app', 'mobile', 'screen', 'button', 'interface', 'navigate', 
                       'create', 'setup', 'view', 'find', 'where', 'how', 'click', 'tap']
        
        if tutorial_results and any(keyword in query.lower() for keyword in app_keywords):
            # Combine text and image analysis for comprehensive guidance
            tutorial_content = []
            for result in tutorial_results[:2]:
                content = result.page_content
                metadata = getattr(result, 'metadata', {})
                
                if metadata.get('content_type') == 'screenshot_analysis':
                    tutorial_content.append(f"📱 **Visual Guide:** {content}")
                else:
                    tutorial_content.append(f"📖 **Instructions:** {content}")
            
            combined_content = "\n\n".join(tutorial_content)
            return f"**Agrisense Mobile App Guide:**\n\n{combined_content}"
        
        # Fallback to general knowledge
        all_results = tutorial_results + general_results + tunisian_results
        if all_results:
            return f"Answer: {all_results[0].page_content}"
        
        return "I couldn't find specific information about that in the tutorial or knowledge base."
        
    except Exception as e:
        return f"Error searching tutorial: {str(e)}"
