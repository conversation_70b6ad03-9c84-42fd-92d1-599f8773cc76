import os
from langchain_core.tools import tool
from magonia.vectore_store_retriever import VectorStoreRetriever
from magonia.enhanced_vector_store import EnhancedVectorStoreRetriever
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Load Redis URL
redis_url = os.getenv("REDIS_URL")

# Create separate retrievers for each document collection
general_retriever = VectorStoreRetriever(redis_url, "general_questions")
tunisian_retriever = VectorStoreRetriever(redis_url, "QuestionTunisian")

# Use enhanced retriever for tutorial content
try:
    enhanced_tutorial_retriever = EnhancedVectorStoreRetriever(redis_url, "tutorial-ENG-enhanced")
except:
    # Fallback to regular retriever if enhanced one fails
    enhanced_tutorial_retriever = VectorStoreRetriever(redis_url, "tutorial-ENG-multimodal")

# Load and embed the documents including enhanced multimodal tutorial
try:
    general_retriever.load_and_embed("general_questions.txt")
    tunisian_retriever.load_and_embed("QuestionTunisian.txt")

    # Try to load enhanced tutorial format first
    if hasattr(enhanced_tutorial_retriever, 'load_enhanced_tutorial'):
        try:
            enhanced_tutorial_retriever.load_enhanced_tutorial("tutorial-ENG-enhanced.txt")
            print(f"Successfully loaded enhanced multimodal tutorial")
        except FileNotFoundError:
            print("Enhanced tutorial file not found, trying to load standard format...")
            if hasattr(enhanced_tutorial_retriever, 'load_tutorial_multimodal'):
                enhanced_tutorial_retriever.load_tutorial_multimodal("tutorial-ENG-multimodal.txt")
            else:
                enhanced_tutorial_retriever.load_and_embed("tutorial-ENG-multimodal.txt")
            print(f"Successfully loaded standard multimodal tutorial")
        except Exception as load_error:
            print(f"Error loading enhanced tutorial: {load_error}")
            # Fallback to basic format
            enhanced_tutorial_retriever.load_and_embed("tutorial-ENG-enhanced.txt")
            print(f"Successfully loaded tutorial with basic format")
    else:
        enhanced_tutorial_retriever.load_and_embed("tutorial-ENG-enhanced.txt")
        print(f"Successfully loaded tutorial with basic format")

except Exception as e:
    print(f"Error loading embeddings: {str(e)}")

# Add a simple fallback search function for tutorial content
def search_tutorial_content(query):
    """Fallback search function that works directly with tutorial file."""
    try:
        import os
        if not os.path.exists("tutorial-ENG-enhanced.txt"):
            return None

        with open("tutorial-ENG-enhanced.txt", 'r', encoding='utf-8') as f:
            content = f.read()

        # Parse Q&A pairs
        sections = content.split("*" * 50)
        qa_pairs = []

        for section in sections:
            section = section.strip()
            if section and "Question:" in section and "Answer:" in section:
                lines = section.split('\n')
                question = ""
                answer_lines = []
                in_answer = False

                for line in lines:
                    if line.startswith("Question:"):
                        question = line.replace("Question:", "").strip()
                        in_answer = False
                    elif line.startswith("Answer:"):
                        answer_lines.append(line.replace("Answer:", "").strip())
                        in_answer = True
                    elif in_answer and line.strip():
                        answer_lines.append(line.strip())

                if question and answer_lines:
                    answer = " ".join(answer_lines)
                    qa_pairs.append((question, answer))

        # Find best matching Q&A
        query_words = set(query.lower().replace('?', '').split())
        best_match = None
        best_score = 0

        for question, answer in qa_pairs:
            question_words = set(question.lower().replace('?', '').split())

            # Calculate similarity
            intersection = len(query_words.intersection(question_words))
            union = len(query_words.union(question_words))
            score = intersection / union if union > 0 else 0

            if score > best_score:
                best_score = score
                best_match = (question, answer)

        if best_match and best_score > 0.1:
            return best_match[1]

        return None

    except Exception as e:
        print(f"Error in fallback search: {e}")
        return None

@tool
def lookup_document_tool(query: str) -> str:
    """
    Search through agricultural knowledge base and multimodal Agrisense tutorial (including UI screenshots).
    
    USE THIS TOOL WHEN:
    - User asks about mobile app interface, navigation, or visual elements
    - User asks "how-to" questions about app operations with step-by-step guidance
    - User needs to understand what buttons, screens, or UI elements to interact with
    - User asks about visual features, icons, or interface layout
    - User wants to know where to find specific functions in the app
    
    EXAMPLE QUERIES:
    - "How do I create a new field in the app?" (will include UI guidance)
    - "What does the irrigation scheduling screen look like?"
    - "Where is the weather data button located?"
    - "How do I navigate to field settings?"
    - "What icons should I look for to add irrigation zones?"
    """
    try:
        # For app-related queries, try fallback search first (our new English content)
        tutorial_results = []

        # Check if this is an app-related query that should use our new English content
        app_related_terms = ['add area', 'create field', 'new area', 'new field', 'trace boundaries', 'add button', 'field management']
        is_app_related = any(term in query.lower() for term in app_related_terms)

        if is_app_related:
            # Try fallback search first for app-related queries
            fallback_result = search_tutorial_content(query)
            if fallback_result:
                # Create a mock result object
                class MockResult:
                    def __init__(self, content):
                        self.page_content = content
                        self.metadata = {'content_type': 'instructions', 'ui_element': 'general'}

                tutorial_results = [MockResult(fallback_result)]
                print(f"Using fallback search for app-related query: {query}")

        # If no fallback result or not app-related, try vector search
        if not tutorial_results:
            try:
                if hasattr(enhanced_tutorial_retriever, 'smart_lookup'):
                    tutorial_results = enhanced_tutorial_retriever.smart_lookup(query, k=3)
                else:
                    tutorial_results = enhanced_tutorial_retriever.lookup_document(query, k=3)
            except Exception as tutorial_error:
                print(f"Tutorial retriever error: {tutorial_error}")
                # Use fallback search as last resort
                if not is_app_related:  # Only if we haven't tried it already
                    fallback_result = search_tutorial_content(query)
                    if fallback_result:
                        class MockResult:
                            def __init__(self, content):
                                self.page_content = content
                                self.metadata = {'content_type': 'instructions', 'ui_element': 'general'}

                        tutorial_results = [MockResult(fallback_result)]

        general_results = []
        tunisian_results = []

        try:
            general_results = general_retriever.lookup_document(query, k=1)
        except:
            pass

        try:
            tunisian_results = tunisian_retriever.lookup_document(query, k=1)
        except:
            pass

        # Enhanced keyword detection for better content prioritization
        ui_keywords = ['app', 'interface', 'screen', 'button', 'menu', 'navigation', 'click', 'tap', 'mobile', 'ui', 'dashboard', 'login']
        feature_keywords = ['field', 'irrigation', 'create', 'add', 'edit', 'view', 'manage', 'schedule', 'monitor', 'control']
        action_keywords = ['how to', 'how do i', 'steps', 'guide', 'tutorial', 'instructions', 'where', 'find', 'setup']

        query_lower = query.lower()
        is_ui_query = any(keyword in query_lower for keyword in ui_keywords)
        is_feature_query = any(keyword in query_lower for keyword in feature_keywords)
        is_action_query = any(keyword in query_lower for keyword in action_keywords)

        # Prioritize tutorial results for app-related questions
        if tutorial_results and (is_ui_query or is_feature_query or is_action_query):
            # Enhanced content formatting based on query type
            tutorial_content = []

            for i, result in enumerate(tutorial_results[:3]):
                content = result.page_content
                metadata = getattr(result, 'metadata', {})

                # Determine content type first
                content_type = metadata.get('content_type', 'general')

                # Check if this is English tutorial content (our new content)
                if any(phrase in content for phrase in ["To add a new area", "Creating a new field", "Navigate to Field Management", "Step-by-Step"]):
                    # This is our new English tutorial content - prioritize it
                    if content_type == 'ui_guidance' or 'screenshot' in content.lower():
                        tutorial_content.append(f"📱 **Visual Guide {i+1}:** {content}")
                    elif content_type == 'instructions' or is_action_query:
                        tutorial_content.append(f"📋 **Step-by-Step {i+1}:** {content}")
                    else:
                        tutorial_content.append(f"📖 **App Instructions {i+1}:** {content}")

                    # Add UI element context if available
                    ui_elements = metadata.get('ui_element', '')
                    if ui_elements and ui_elements != 'general':
                        tutorial_content.append(f"🎯 **Related UI Elements:** {ui_elements.replace(',', ', ')}")
                else:
                    # This is older content - format differently
                    content_type = metadata.get('content_type', 'general')
                    tutorial_content.append(f"📝 **Additional Info {i+1}:** {content}")

            if tutorial_content:
                combined_content = "\n\n".join(tutorial_content)

                # Add helpful footer based on query type
                footer = ""
                if is_ui_query:
                    footer = "\n\n💡 **Tip:** Look for visual indicators and follow the on-screen prompts in the app."
                elif is_action_query:
                    footer = "\n\n💡 **Tip:** Follow the steps in order and don't hesitate to use the app's help features."

                return f"**Agrisense Mobile App Guide:**\n\n{combined_content}{footer}"
        
        # Fallback to general knowledge
        all_results = tutorial_results + general_results + tunisian_results
        if all_results:
            return f"Answer: {all_results[0].page_content}"
        
        return "I couldn't find specific information about that in the tutorial or knowledge base."
        
    except Exception as e:
        return f"Error searching tutorial: {str(e)}"
