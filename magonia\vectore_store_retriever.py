import os
from langchain_openai import OpenAIEmbeddings
from langchain_redis import RedisConfig, RedisVectorStore


class VectorStoreRetriever:
    def __init__(self, redis_url, index_name):
        if not redis_url:
            raise ValueError("The REDIS_URL environment variable is not set or is invalid.")

        self.embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
        self.config = RedisConfig(
            index_name=index_name,
            redis_url=redis_url,
            metadata_schema=[{"name": "category", "type": "tag"}],
        )
        self.vector_store = RedisVectorStore(self.embeddings, config=self.config)

    def load_and_embed(self, file_path):
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Split the content by the delimiter
        entries = content.split("*********")
        texts = []

        for entry in entries:
            entry = entry.strip()
            if entry:
                parts = entry.split("Réponse:")
                if len(parts) == 2:
                    question = parts[0].replace("Question:", "").strip()
                    answer = parts[1].strip()
                    texts.append(f"{question} {answer}")

        if not texts:
            raise ValueError("No valid question-answer pairs found in the file.")

        metadata = [{"category": "general"}] * len(texts)
        ids = self.vector_store.add_texts(texts, metadata)
        return ids

    def lookup_document(self, query, k=2):
        results = self.vector_store.similarity_search(query, k=k)
        return results

    def load_tutorial_multimodal(self, file_path):
        """Load multimodal tutorial content with enhanced chunking."""
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Split by page sections and screenshot analyses
        sections = content.split("*" * 50)
        texts = []
        metadata = []

        for section in sections:
            section = section.strip()
            if section and len(section) > 50:  # Filter out very short sections
            
                # Determine content type and extract metadata
                if "### Screenshot Analysis" in section:
                    # This is an image description section
                    page_match = section.split("## Page")[1].split("\n")[0] if "## Page" in section else "Unknown"
                    texts.append(section)
                    metadata.append({
                        "category": "tutorial_image", 
                        "page": page_match.strip(),
                        "content_type": "screenshot_analysis"
                    })
                elif "## Page" in section:
                    # This is a text section
                    page_match = section.split("## Page")[1].split("\n")[0] if "## Page" in section else "Unknown"
                    texts.append(section)
                    metadata.append({
                        "category": "tutorial_text",
                        "page": page_match.strip(), 
                        "content_type": "instructional_text"
                    })
                else:
                    # General content
                    texts.append(section)
                    metadata.append({
                        "category": "tutorial_general",
                        "content_type": "mixed"
                    })

        if not texts:
            raise ValueError("No valid tutorial content found in the file.")

        ids = self.vector_store.add_texts(texts, metadata)
        return ids

